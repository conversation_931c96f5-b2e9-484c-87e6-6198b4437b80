<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播订单管理</title>

    <script src="https://registry.npmmirror.com/tailwindcss-cdn/3.4.10/files/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/xlsx.full.min.js" defer></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <script src="js/common-utils.js"></script>
    <script src="js/extract-orders.js" defer></script>
    <style>
        .table-container {
            overflow-x: auto;
        }

        @media (max-width: 640px) {
            .pagination-desktop {
                display: none;
            }

            .pagination-mobile {
                display: flex;
            }
        }

        @media (min-width: 641px) {
            .pagination-desktop {
                display: flex;
            }

            .pagination-mobile {
                display: none;
            }
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .sort-icon {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin-left: 5px;
        }

        .sort-asc::after {
            content: '▲';
            font-size: 10px;
        }

        .sort-desc::after {
            content: '▼';
            font-size: 10px;
        }

        th.sortable {
            cursor: pointer;
        }

        th.sortable:hover {
            background-color: #f3f4f6;
        }

        .content-width {
            width: 80%;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-paid {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-refunded {
            background-color: #fee2e2;
            color: #dc2626;
        }

        /* 主播按钮样式 */
        .anchor-btn {
            padding: 6px 16px;
            border: 2px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .anchor-btn:hover {
            border-color: #ea580c;
            color: #ea580c;
            background-color: #fff7ed;
        }

        .anchor-btn-active {
            border-color: #ea580c;
            background-color: #ea580c;
            color: #ffffff;
        }

        .anchor-btn-active:hover {
            border-color: #dc2626;
            background-color: #dc2626;
            color: #ffffff;
        }

        /* 时间按钮样式 */
        .time-btn {
            padding: 6px 16px;
            border: 2px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .time-btn:hover {
            border-color: #4f46e5;
            color: #4f46e5;
            background-color: #eef2ff;
        }

        .time-btn-active {
            border-color: #4f46e5;
            background-color: #4f46e5;
            color: #ffffff;
        }

        .time-btn-active:hover {
            border-color: #3730a3;
            background-color: #3730a3;
            color: #ffffff;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-orange-600 shadow">
        <div class="content-width mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-xl font-bold text-white">直播订单管理</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="live-plans.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-video mr-1"></i>直播计划
                    </a>
                    <a href="live-orders.html" class="bg-orange-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-shopping-cart mr-1"></i>直播订单
                    </a>
                    <a href="task-manager.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-tasks mr-1"></i>任务管理
                    </a>
                    <a href="gpu-accounts.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-server mr-1"></i>GPU账号
                    </a>
                    <a href="anchors.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-microphone mr-1"></i>主播管理
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="content-width mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 筛选表单 -->
        <div class="bg-white shadow rounded-lg mb-6 p-4">
            <form id="filterForm" class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                <!-- 主播筛选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">主播筛选</label>
                    <div id="anchorButtons" class="flex flex-wrap gap-2">
                        <button type="button" class="anchor-btn anchor-btn-active" data-anchor="">
                            全部
                        </button>
                        <!-- 主播按钮将通过JavaScript动态生成 -->
                    </div>
                    <input type="hidden" id="anchorFilter" name="anchor" value="">
                </div>

                <!-- 时间筛选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间筛选</label>
                    <div id="timeButtons" class="flex flex-wrap gap-2">
                        <button type="button" class="time-btn time-btn-active" data-days="">
                            全部
                        </button>
                        <button type="button" class="time-btn" data-days="1">
                            今天
                        </button>
                        <button type="button" class="time-btn" data-days="3">
                            3天内
                        </button>
                        <button type="button" class="time-btn" data-days="7">
                            7天内
                        </button>
                        <button type="button" class="time-btn" data-days="30">
                            30天内
                        </button>
                    </div>
                    <input type="hidden" id="timeFilter" name="timeRange" value="">
                </div>

                <!-- 支付时间范围 -->
                <div class="sm:col-span-3">
                    <label for="startDate" class="block text-sm font-medium text-gray-700">开始时间</label>
                    <div class="mt-1">
                        <input type="date" id="startDate" name="startDate"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <div class="sm:col-span-3">
                    <label for="endDate" class="block text-sm font-medium text-gray-700">结束时间</label>
                    <div class="mt-1">
                        <input type="date" id="endDate" name="endDate"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 商品搜索 -->
                <div class="sm:col-span-2">
                    <label for="itemSearch" class="block text-sm font-medium text-gray-700">商品搜索</label>
                    <div class="mt-1">
                        <input type="text" id="itemSearch" name="itemSearch" placeholder="输入商品标题或ID"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 直播ID -->
                <div class="sm:col-span-2">
                    <label for="liveIdFilter" class="block text-sm font-medium text-gray-700">直播ID</label>
                    <div class="mt-1">
                        <input type="text" id="liveIdFilter" name="liveId" placeholder="输入直播ID"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 订单金额范围 -->
                <div class="sm:col-span-1">
                    <label for="minAmount" class="block text-sm font-medium text-gray-700">最小金额</label>
                    <div class="mt-1">
                        <input type="number" id="minAmount" name="minAmount" placeholder="0" step="0.01"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <div class="sm:col-span-1">
                    <label for="maxAmount" class="block text-sm font-medium text-gray-700">最大金额</label>
                    <div class="mt-1">
                        <input type="number" id="maxAmount" name="maxAmount" placeholder="99999" step="0.01"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="sm:col-span-6 flex justify-between items-center">
                    <div class="flex items-center">
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-search mr-2"></i> 查询
                        </button>
                        <button type="button" id="resetBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-redo mr-2"></i> 重置
                        </button>
                        <button type="button" id="exportBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-download mr-2"></i> 导出Excel
                        </button>
                        <button type="button" id="extractOrdersBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                            <i class="fas fa-shopping-cart mr-2"></i> 提取订单
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-blue-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-blue-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总订单数</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalOrders">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-green-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-green-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总销售额</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalSales">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-yellow-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-calculator text-yellow-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">平均客单价</p>
                        <p class="text-2xl font-semibold text-gray-700" id="avgOrderValue">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-red-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-undo text-red-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">退款金额</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalRefunds">--</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="bg-white shadow overflow-hidden rounded-lg">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">直播订单列表</h3>
                <div class="flex items-center">
                    <div class="text-sm text-gray-500 mr-4">共 <span id="totalCount">0</span> 条记录</div>
                </div>
            </div>
            <div class="border-t border-gray-200 table-container">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('child_order_id')">
                                订单号 <span id="sort-child_order_id" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                主播名称
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                直播ID
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                商品标题
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                商品ID
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('pay_time')">
                                支付时间 <span id="sort-pay_time" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('live_start_time')">
                                直播开始时间 <span id="sort-live_start_time" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('pay_days')">
                                支付天数 <span id="sort-pay_days" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('pay_amount')">
                                支付金额 <span id="sort-pay_amount" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('refund_amount')">
                                退款金额 <span id="sort-refund_amount" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                数量
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                净金额
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('created_at')">
                                创建时间 <span id="sort-created_at" class="sort-icon"></span>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="ordersTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="13" class="px-6 py-4 text-center text-sm text-gray-500">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <!-- 移动端分页 -->
                <div class="pagination-mobile flex-1 flex justify-between">
                    <button id="prevPageMobile"
                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <div class="text-sm text-gray-700 flex items-center">
                        第 <span id="currentPageMobile" class="mx-1">1</span> / <span id="totalPagesMobile" class="mx-1">1</span> 页
                    </div>
                    <button id="nextPageMobile"
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <!-- 桌面端分页 -->
                <div class="pagination-desktop flex-1 sm:flex sm:items-center sm:justify-between ml-4">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startItem" class="font-medium">1</span> 到第
                            <span id="endItem" class="font-medium">10</span> 条，共
                            <span id="totalItems" class="font-medium">0</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination" id="pagination">
                            <!-- 分页按钮将通过JavaScript填充 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 访问密码输入弹窗 -->
    <div id="apiKeyModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                请输入访问密码
                            </h3>
                            <div class="mt-4">
                                <form id="apiKeyForm">
                                    <div class="mb-4">
                                        <label for="apiKey" class="block text-sm font-medium text-gray-700">访问密码</label>
                                        <input type="password" name="apiKey" id="apiKey"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                                            required>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="submitApiKeyBtn" type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm">
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提取订单弹窗 -->
    <div id="extractOrdersModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">

                <!-- 弹窗头部 -->
                <div class="bg-white px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            提取订单数据
                        </h3>
                        <button type="button" onclick="hideExtractOrdersModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 弹窗内容 -->
                <div class="bg-white px-6 py-4">
                    <!-- 时间段选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">选择时间段</label>

                        <!-- 开始时间 -->
                        <div class="mb-3">
                            <label class="block text-xs font-medium text-gray-600 mb-1">开始时间</label>
                            <div class="flex gap-2">
                                <input type="date" id="orderStartDate" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                                <input type="time" id="orderStartTime" value="00:00" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                            </div>
                        </div>

                        <!-- 结束时间 -->
                        <div class="mb-4">
                            <label class="block text-xs font-medium text-gray-600 mb-1">结束时间</label>
                            <div class="flex gap-2">
                                <input type="date" id="orderEndDate" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                                <input type="time" id="orderEndTime" value="23:59" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                            </div>
                        </div>
                    </div>

                    <!-- 快捷选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">快捷选择</label>
                        <div class="grid grid-cols-2 gap-2">
                            <button type="button" class="quick-select-btn" data-days="3">
                                <i class="fas fa-calendar-alt mr-2"></i>3天
                            </button>
                            <button type="button" class="quick-select-btn" data-days="7">
                                <i class="fas fa-calendar-week mr-2"></i>7天
                            </button>
                            <button type="button" class="quick-select-btn" data-days="15">
                                <i class="fas fa-calendar mr-2"></i>15天
                            </button>
                            <button type="button" class="quick-select-btn" data-days="30">
                                <i class="fas fa-calendar-check mr-2"></i>30天
                            </button>
                        </div>
                    </div>

                    <!-- 主播选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">选择主播</label>
                        <div id="orderAnchorButtons" class="grid grid-cols-3 gap-2">
                            <button type="button" class="order-anchor-btn order-anchor-btn-active" data-anchor="">
                                全部主播
                            </button>
                            <!-- 主播按钮将通过JavaScript动态生成 -->
                        </div>
                        <input type="hidden" id="orderAnchorSelect" value="">
                    </div>
                </div>

                <!-- 弹窗底部 -->
                <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                    <button type="button" onclick="hideExtractOrdersModal()"
                        class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" id="confirmExtractOrders"
                        class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-emerald-600 text-base font-medium text-white hover:bg-emerald-700">
                        提取订单
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .quick-select-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-select-btn:hover {
            border-color: #10b981;
            background-color: #ecfdf5;
            color: #047857;
        }

        .quick-select-btn.active {
            border-color: #10b981;
            background-color: #10b981;
            color: #ffffff;
        }

        /* 订单提取主播按钮样式 */
        .order-anchor-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .order-anchor-btn:hover {
            border-color: #ea580c;
            background-color: #fff7ed;
            color: #ea580c;
        }

        .order-anchor-btn-active {
            border-color: #ea580c;
            background-color: #ea580c;
            color: #ffffff;
        }

        .order-anchor-btn-active:hover {
            border-color: #dc2626;
            background-color: #dc2626;
            color: #ffffff;
        }
    </style>

    <script src="js/live-orders.js"></script>
</body>
</html> 